import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowLeft, Globe, Users, ShoppingCart, BarChart4, Shield, Zap } from 'lucide-react';

function EcommercePage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <nav className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <Link to="/" className="flex items-center text-gray-600 hover:text-gray-900">
            <ArrowLeft className="w-5 h-5 mr-2" />
            Back to Portfolio
          </Link>
        </div>
      </nav>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="bg-white rounded-lg shadow-lg overflow-hidden">
          <img
            src="https://images.unsplash.com/photo-1557821552-17105176677c?auto=format&fit=crop&w=1600&h=600"
            alt="E-commerce Platform"
            className="w-full h-96 object-cover"
          />
          
          <div className="p-8">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">E-commerce Platform</h1>
            
            <div className="flex flex-wrap gap-2 mb-8">
              <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">Next.js</span>
              <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">Node.js</span>
              <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">PostgreSQL</span>
              <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">Stripe</span>
            </div>

            <div className="prose max-w-none">
              <p className="text-lg text-gray-600 mb-8">
                A comprehensive e-commerce solution built for scalability and performance. This platform provides
                businesses with everything they need to run their online store effectively, from inventory
                management to payment processing and analytics.
              </p>

              <h2 className="text-2xl font-bold text-gray-900 mb-4">Key Features</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                <FeatureCard
                  icon={<Globe className="w-6 h-6" />}
                  title="Multi-tenant Architecture"
                  description="Support for multiple stores with isolated data and customizable themes"
                />
                <FeatureCard
                  icon={<ShoppingCart className="w-6 h-6" />}
                  title="Real-time Inventory"
                  description="Live stock updates and automated reordering system"
                />
                <FeatureCard
                  icon={<Users className="w-6 h-6" />}
                  title="Customer Management"
                  description="Detailed customer profiles and purchase history tracking"
                />
                <FeatureCard
                  icon={<BarChart4 className="w-6 h-6" />}
                  title="Analytics Dashboard"
                  description="Comprehensive sales, inventory, and customer behavior analytics"
                />
                <FeatureCard
                  icon={<Shield className="w-6 h-6" />}
                  title="Secure Payments"
                  description="PCI-compliant payment processing with Stripe integration"
                />
                <FeatureCard
                  icon={<Zap className="w-6 h-6" />}
                  title="Performance Optimized"
                  description="Fast page loads with server-side rendering and caching"
                />
              </div>

              <h2 className="text-2xl font-bold text-gray-900 mb-4">Technical Details</h2>
              <ul className="list-disc pl-6 text-gray-600 mb-8">
                <li>Next.js frontend with server-side rendering for optimal performance</li>
                <li>Node.js backend with Express for REST API endpoints</li>
                <li>PostgreSQL database with robust data modeling</li>
                <li>Redis caching layer for improved performance</li>
                <li>Stripe integration for secure payment processing</li>
                <li>Docker containerization for consistent deployments</li>
                <li>AWS infrastructure with auto-scaling capabilities</li>
              </ul>

              <h2 className="text-2xl font-bold text-gray-900 mb-4">Results</h2>
              <ul className="list-disc pl-6 text-gray-600">
                <li>Processed over $1M in transactions within first 3 months</li>
                <li>99.99% uptime with zero security incidents</li>
                <li>Average page load time under 1.5 seconds</li>
                <li>50% reduction in operational costs for clients</li>
              </ul>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}

interface FeatureCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
}

function FeatureCard({ icon, title, description }: FeatureCardProps) {
  return (
    <div className="bg-gray-50 p-6 rounded-lg">
      <div className="text-blue-600 mb-3">{icon}</div>
      <h3 className="text-lg font-semibold text-gray-900 mb-2">{title}</h3>
      <p className="text-gray-600">{description}</p>
    </div>
  );
}

export default EcommercePage;