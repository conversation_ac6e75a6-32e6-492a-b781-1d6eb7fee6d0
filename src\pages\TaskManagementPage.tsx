import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { ArrowLeft, MessageSquare, FileText, Clock, Bell, Users, Zap } from 'lucide-react';

function TaskManagementPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <nav className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <Link to="/" className="flex items-center text-gray-600 hover:text-gray-900">
            <ArrowLeft className="w-5 h-5 mr-2" />
            Back to Portfolio
          </Link>
        </div>
      </nav>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="bg-white rounded-lg shadow-lg overflow-hidden">
          <img
            src="https://images.unsplash.com/photo-1507925921958-8a62f3d1a50d?auto=format&fit=crop&w=1600&h=600"
            alt="Task Management System"
            className="w-full h-96 object-cover"
          />
          
          <div className="p-8">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">Task Management System</h1>
            
            <div className="flex flex-wrap gap-2 mb-8">
              <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">React</span>
              <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">Socket.io</span>
              <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">MongoDB</span>
              <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">Express</span>
            </div>

            <div className="prose max-w-none">
              <p className="text-lg text-gray-600 mb-8">
                A real-time collaborative task management system designed for modern teams. This platform
                enables seamless collaboration with features like real-time updates, file sharing, and
                team communication tools, all in one integrated solution.
              </p>

              <h2 className="text-2xl font-bold text-gray-900 mb-4">Key Features</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                <FeatureCard
                  icon={<MessageSquare className="w-6 h-6" />}
                  title="Real-time Chat"
                  description="Integrated team chat with thread support and file sharing"
                />
                <FeatureCard
                  icon={<FileText className="w-6 h-6" />}
                  title="Document Collaboration"
                  description="Real-time document editing and version control"
                />
                <FeatureCard
                  icon={<Clock className="w-6 h-6" />}
                  title="Time Tracking"
                  description="Built-in time tracking with detailed reporting"
                />
                <FeatureCard
                  icon={<Bell className="w-6 h-6" />}
                  title="Smart Notifications"
                  description="Customizable notifications and reminders"
                />
                <FeatureCard
                  icon={<Users className="w-6 h-6" />}
                  title="Team Management"
                  description="Role-based access control and team organization"
                />
                <FeatureCard
                  icon={<Zap className="w-6 h-6" />}
                  title="Automation"
                  description="Workflow automation and task dependencies"
                />
              </div>

              <h2 className="text-2xl font-bold text-gray-900 mb-4">Technical Details</h2>
              <ul className="list-disc pl-6 text-gray-600 mb-8">
                <li>React frontend with Redux for state management</li>
                <li>Socket.io for real-time communication</li>
                <li>MongoDB for flexible data storage</li>
                <li>Express.js backend with RESTful API</li>
                <li>JWT-based authentication system</li>
                <li>WebSocket protocol for live updates</li>
                <li>Microservices architecture for scalability</li>
              </ul>

              <h2 className="text-2xl font-bold text-gray-900 mb-4">Results</h2>
              <ul className="list-disc pl-6 text-gray-600">
                <li>Over 10,000 active daily users</li>
                <li>30% improvement in team productivity</li>
                <li>25% reduction in meeting time</li>
                <li>98% user satisfaction rate</li>
              </ul>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}

interface FeatureCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
}

function FeatureCard({ icon, title, description }: FeatureCardProps) {
  return (
    <div className="bg-gray-50 p-6 rounded-lg">
      <div className="text-blue-600 mb-3">{icon}</div>
      <h3 className="text-lg font-semibold text-gray-900 mb-2">{title}</h3>
      <p className="text-gray-600">{description}</p>
    </div>
  );
}

export default TaskManagementPage;