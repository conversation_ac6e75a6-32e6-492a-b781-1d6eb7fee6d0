import React from 'react';
import { G<PERSON>ub, Linkedin, Mail, Code2, Database, Layout, Server, Braces, Globe, Award, Briefcase } from 'lucide-react';
import { Link } from 'react-router-dom';

function HomePage() {
  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-50 to-gray-100">
      {/* Header/Hero Section */}
      <header className="bg-white shadow-sm">
        <div className="max-w-5xl mx-auto px-4 py-16 sm:px-6 lg:px-8">
          <div className="text-center">
            <img
              className="w-32 h-32 rounded-full mx-auto mb-6 shadow-lg"
              src="https://images.unsplash.com/photo-1568602471122-7832951cc4c5?auto=format&fit=crop&w=300&h=300"
              alt="Profile"
            />
            <h1 className="text-4xl font-bold text-gray-900 mb-2"><PERSON></h1>
            <p className="text-xl text-gray-600 mb-6">Full Stack Software Engineer</p>
            <div className="flex justify-center space-x-4">
              <a href="#" className="text-gray-600 hover:text-gray-900">
                <Github className="w-6 h-6" />
              </a>
              <a href="#" className="text-gray-600 hover:text-gray-900">
                <Linkedin className="w-6 h-6" />
              </a>
              <a href="#" className="text-gray-600 hover:text-gray-900">
                <Mail className="w-6 h-6" />
              </a>
            </div>
          </div>
        </div>
      </header>

      {/* About Section */}
      <section className="py-16 bg-white">
        <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">About Me</h2>
          <p className="text-lg text-gray-600 leading-relaxed">
            I'm a passionate software engineer with 5+ years of experience building scalable web applications
            and distributed systems. I specialize in full-stack development with a focus on cloud-native
            solutions and modern JavaScript frameworks. When I'm not coding, you can find me contributing
            to open-source projects or mentoring aspiring developers.
          </p>
        </div>
      </section>

      {/* Employment Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-12 text-center">Employment History</h2>
          <div className="space-y-12">
            <div className="relative pl-8 border-l-2 border-blue-500">
              <div className="absolute w-4 h-4 bg-blue-500 rounded-full -left-[9px] top-0"></div>
              <div className="bg-white p-6 rounded-lg shadow-md">
                <div className="flex items-center mb-2">
                  <Briefcase className="w-5 h-5 text-blue-600 mr-2" />
                  <h3 className="text-xl font-semibold text-gray-900">Senior Software Engineer</h3>
                </div>
                <p className="text-blue-600 font-medium mb-2">TechCorp Solutions • 2021 - Present</p>
                <ul className="list-disc list-inside text-gray-600 space-y-2">
                  <li>Led development of cloud-native microservices architecture</li>
                  <li>Reduced system latency by 40% through optimization</li>
                  <li>Mentored junior developers and conducted code reviews</li>
                </ul>
              </div>
            </div>

            <div className="relative pl-8 border-l-2 border-blue-500">
              <div className="absolute w-4 h-4 bg-blue-500 rounded-full -left-[9px] top-0"></div>
              <div className="bg-white p-6 rounded-lg shadow-md">
                <div className="flex items-center mb-2">
                  <Briefcase className="w-5 h-5 text-blue-600 mr-2" />
                  <h3 className="text-xl font-semibold text-gray-900">Full Stack Developer</h3>
                </div>
                <p className="text-blue-600 font-medium mb-2">InnovateTech • 2019 - 2021</p>
                <ul className="list-disc list-inside text-gray-600 space-y-2">
                  <li>Developed and maintained multiple client-facing applications</li>
                  <li>Implemented CI/CD pipelines reducing deployment time by 60%</li>
                  <li>Collaborated with UX team to improve user experience</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Certificates Section */}
      <section className="py-16 bg-white">
        <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-12 text-center">Certificates</h2>
          <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg shadow-lg p-6 flex items-center">
            <div className="bg-white rounded-full p-3 mr-6">
              <Award className="w-8 h-8 text-blue-600" />
            </div>
            <div>
              <h3 className="text-xl font-semibold text-white mb-2">AWS Certified Cloud Practitioner</h3>
              <p className="text-blue-100">Issued by Amazon Web Services • 2023</p>
            </div>
          </div>
        </div>
      </section>

      {/* Skills Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-12 text-center">Technical Skills</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <SkillCard
              icon={<Code2 className="w-8 h-8" />}
              title="Frontend Development"
              skills={['React', 'TypeScript', 'Next.js', 'Tailwind CSS']}
            />
            <SkillCard
              icon={<Server className="w-8 h-8" />}
              title="Backend Development"
              skills={['Node.js', 'Python', 'Go', 'RESTful APIs']}
            />
            <SkillCard
              icon={<Database className="w-8 h-8" />}
              title="Databases"
              skills={['PostgreSQL', 'MongoDB', 'Redis', 'Elasticsearch']}
            />
            <SkillCard
              icon={<Layout className="w-8 h-8" />}
              title="DevOps"
              skills={['Docker', 'Kubernetes', 'AWS', 'CI/CD']}
            />
            <SkillCard
              icon={<Braces className="w-8 h-8" />}
              title="Programming Languages"
              skills={['JavaScript', 'TypeScript', 'Python', 'Go']}
            />
            <SkillCard
              icon={<Globe className="w-8 h-8" />}
              title="Other Skills"
              skills={['System Design', 'Agile', 'Git', 'Testing']}
            />
          </div>
        </div>
      </section>

      {/* Projects Section */}
      <section className="py-16 bg-white">
        <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-12 text-center">Featured Projects</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <Link to="/projects/xray-detection" className="block hover:transform hover:scale-105 transition-transform duration-200">
              <ProjectCard
                title="X-Ray Pneumonia Detection"
                description="AI-powered diagnostic tool using deep learning to automatically detect pneumonia from chest X-ray images with high accuracy."
                tags={['Python', 'TensorFlow', 'Deep Learning', 'Medical AI']}
                image="https://images.unsplash.com/photo-1576091160550-2173dba999ef?auto=format&fit=crop&w=800&h=500"
              />
            </Link>
            <Link to="/projects/netcommhub" className="block hover:transform hover:scale-105 transition-transform duration-200">
              <ProjectCard
                title="NetCommHub"
                description="A robust networked distributed system for group-based client-server communication with a graphical user interface."
                tags={['Java', 'Networking', 'Distributed Systems', 'GUI']}
                image="https://images.unsplash.com/photo-**********-b99a580bb7a8?auto=format&fit=crop&w=800&h=500"
              />
            </Link>
            <Link to="/projects/study-platform" className="block hover:transform hover:scale-105 transition-transform duration-200">
              <ProjectCard
                title="AI Study Platform"
                description="An advanced educational platform that transforms lecture materials into summarized content and interactive quizzes using AI."
                tags={['React', 'TypeScript', 'AWS', 'AI/ML']}
                image="https://images.unsplash.com/photo-1501504905252-473c47e087f8?auto=format&fit=crop&w=800&h=500"
              />
            </Link>
            <Link to="/projects/text-summarization" className="block hover:transform hover:scale-105 transition-transform duration-200">
              <ProjectCard
                title="DFO Text Summarization"
                description="Python implementation of extractive text summarization using Derivative-Free Optimization algorithms for scientific papers."
                tags={['Python', 'NLP', 'ML', 'Optimization']}
                image="https://images.unsplash.com/photo-1456324504439-367cee3b3c32?auto=format&fit=crop&w=800&h=500"
              />
            </Link>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-8">
        <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <p className="text-gray-400">© 2024 Alex Chen. All rights reserved.</p>
        </div>
      </footer>
    </div>
  );
}

interface SkillCardProps {
  icon: React.ReactNode;
  title: string;
  skills: string[];
}

function SkillCard({ icon, title, skills }: SkillCardProps) {
  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <div className="flex items-center mb-4">
        <div className="text-blue-600 mr-3">{icon}</div>
        <h3 className="text-xl font-semibold text-gray-900">{title}</h3>
      </div>
      <ul className="space-y-2">
        {skills.map((skill) => (
          <li key={skill} className="text-gray-600">{skill}</li>
        ))}
      </ul>
    </div>
  );
}

interface ProjectCardProps {
  title: string;
  description: string;
  tags: string[];
  image: string;
}

function ProjectCard({ title, description, tags, image }: ProjectCardProps) {
  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden">
      <img src={image} alt={title} className="w-full h-48 object-cover" />
      <div className="p-6">
        <h3 className="text-xl font-semibold text-gray-900 mb-2">{title}</h3>
        <p className="text-gray-600 mb-4">{description}</p>
        <div className="flex flex-wrap gap-2">
          {tags.map((tag) => (
            <span
              key={tag}
              className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm"
            >
              {tag}
            </span>
          ))}
        </div>
      </div>
    </div>
  );
}

export default HomePage;